package kl.nbase.timer.lock;

import kl.nbase.timer.config.DbLockConfig;
import kl.nbase.timer.config.TimerConfig;
import kl.nbase.timer.lock.entity.TaskLockEntity;
import kl.nbase.timer.lock.repository.impl.JDBCTaskLockRepositoryImpl;
import kl.nbase.timer.lock.util.DatabaseCompatibilityUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.time.ZoneOffset;

import static org.junit.jupiter.api.Assertions.*;

/**
 * @Author: guoq
 * @Date: 2023/12/07
 * @description: 增强版JDBC任务锁测试类
 */
public class EnhancedJDBCTaskLockTest {

    private JDBCTaskLockRepositoryImpl lockRepository;
    private TimerConfig timerConfig;
    private Connection connection;

    @BeforeEach
    public void setUp() throws SQLException {
        // 使用H2内存数据库进行测试
        connection = DriverManager.getConnection("jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1", "sa", "");

        // 创建锁表
        DatabaseCompatibilityUtil.createLockTableIfNotExists(connection);

        // 配置TimerConfig
        timerConfig = new TimerConfig();
        DbLockConfig dbLockConfig = new DbLockConfig();
        dbLockConfig.setDbUrl("jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1");
        dbLockConfig.setUsername("sa");
        dbLockConfig.setPassword("");
        timerConfig.setDbLock(dbLockConfig);

        // 配置时间锁
        DbLockConfig timeLockConfig = new DbLockConfig();
        timeLockConfig.setUseUtcTime(true); // 使用UTC时间进行测试
        timeLockConfig.setTimePrecision(DbLockConfig.TimePrecision.MILLISECOND);
        timerConfig.setDbLock(timeLockConfig);

        // 创建增强版锁仓库
        lockRepository = new JDBCTaskLockRepositoryImpl(
                timerConfig, null);
    }

    @Test
    public void testTryLock_Success() {
        TaskLockEntity lockEntity = new TaskLockEntity("test-task-1", "Test Task 1");

        boolean result = lockRepository.tryLock(lockEntity);
        assertTrue(result, "First lock attempt should succeed");

        // 验证锁信息
        TaskLockEntity lockInfo = lockRepository.getLockInfo("test-task-1");
        assertNotNull(lockInfo);
        assertEquals("test-task-1", lockInfo.getTaskId());
        assertEquals("Test Task 1", lockInfo.getTaskName());
        assertNotNull(lockInfo.getExpiredTime());
        assertNotNull(lockInfo.getCreateTime());
        assertNotNull(lockInfo.getUpdateTime());
    }

    @Test
    public void testTryLock_Duplicate() {
        TaskLockEntity lockEntity1 = new TaskLockEntity("test-task-2", "Test Task 2");
        TaskLockEntity lockEntity2 = new TaskLockEntity("test-task-2", "Test Task 2 Duplicate");

        boolean result1 = lockRepository.tryLock(lockEntity1);
        assertTrue(result1, "First lock attempt should succeed");

        boolean result2 = lockRepository.tryLock(lockEntity2);
        assertFalse(result2, "Second lock attempt should fail due to duplicate key");
    }

    @Test
    public void testDelayLock() {
        TaskLockEntity lockEntity = new TaskLockEntity("test-task-3", "Test Task 3");

        // 先获取锁
        boolean lockResult = lockRepository.tryLock(lockEntity);
        assertTrue(lockResult);

        // 获取原始过期时间
        TaskLockEntity originalLock = lockRepository.getLockInfo("test-task-3");
        LocalDateTime originalExpiredTime = originalLock.getExpiredTime();

        // 延期锁
        lockEntity.setExpiredTime(originalExpiredTime.plusMinutes(5));
        lockEntity.setVersion(lockEntity.getVersion() + 1);
        lockEntity.setUpdateTime(LocalDateTime.now(ZoneOffset.UTC));

        boolean delayResult = lockRepository.delayLock(lockEntity);
        assertTrue(delayResult, "Delay lock should succeed");

        // 验证延期后的锁信息
        TaskLockEntity delayedLock = lockRepository.getLockInfo("test-task-3");
        assertNotNull(delayedLock);
        assertTrue(delayedLock.getExpiredTime().isAfter(originalExpiredTime),
                "Expired time should be extended");
        assertEquals(1, delayedLock.getVersion(), "Version should be incremented");
    }

    @Test
    public void testUnlock() {
        TaskLockEntity lockEntity = new TaskLockEntity("test-task-4", "Test Task 4");

        // 先获取锁
        boolean lockResult = lockRepository.tryLock(lockEntity);
        assertTrue(lockResult);

        // 验证锁存在
        TaskLockEntity lockInfo = lockRepository.getLockInfo("test-task-4");
        assertNotNull(lockInfo);

        // 释放锁
        boolean unlockResult = lockRepository.unlock("test-task-4");
        assertTrue(unlockResult, "Unlock should succeed");

        // 验证锁已被删除
        TaskLockEntity deletedLock = lockRepository.getLockInfo("test-task-4");
        assertNull(deletedLock, "Lock should be deleted");
    }

    @Test
    public void testUnlockWithServiceId() {
        TaskLockEntity lockEntity = new TaskLockEntity("test-task-5", "Test Task 5");
        String serviceId = lockEntity.getServiceId();

        // 先获取锁
        boolean lockResult = lockRepository.tryLock(lockEntity);
        assertTrue(lockResult);

        // 使用错误的serviceId尝试释放锁
        boolean wrongUnlockResult = lockRepository.unlock("test-task-5", "wrong-service-id");
        assertFalse(wrongUnlockResult, "Unlock with wrong service ID should fail");

        // 验证锁仍然存在
        TaskLockEntity lockInfo = lockRepository.getLockInfo("test-task-5");
        assertNotNull(lockInfo);

        // 使用正确的serviceId释放锁
        boolean correctUnlockResult = lockRepository.unlock("test-task-5", serviceId);
        assertTrue(correctUnlockResult, "Unlock with correct service ID should succeed");

        // 验证锁已被删除
        TaskLockEntity deletedLock = lockRepository.getLockInfo("test-task-5");
        assertNull(deletedLock, "Lock should be deleted");
    }

    @Test
    public void testCleanupExpiredLocks() throws InterruptedException {
        // 创建一个即将过期的锁
        TaskLockEntity lockEntity = new TaskLockEntity("test-task-6", "Test Task 6");
        LocalDateTime now = LocalDateTime.now(ZoneOffset.UTC);
        lockEntity.setExpiredTime(now.minusSeconds(1)); // 设置为1秒前过期
        lockEntity.setCreateTime(now.minusMinutes(1));
        lockEntity.setUpdateTime(now.minusMinutes(1));

        boolean lockResult = lockRepository.tryLock(lockEntity);
        assertTrue(lockResult);

        // 验证锁存在
        TaskLockEntity lockInfo = lockRepository.getLockInfo("test-task-6");
        assertNotNull(lockInfo);

        // 清理过期锁
        int cleanedCount = lockRepository.cleanupExpiredLocks();
        assertEquals(1, cleanedCount, "Should clean up 1 expired lock");

        // 验证锁已被清理
        TaskLockEntity cleanedLock = lockRepository.getLockInfo("test-task-6");
        assertNull(cleanedLock, "Expired lock should be cleaned up");
    }

    @Test
    public void testTimePrecisionHandling() {
        TaskLockEntity lockEntity = new TaskLockEntity("test-task-7", "Test Task 7");

        // 设置包含毫秒的时间
        LocalDateTime timeWithMillis = LocalDateTime.now(ZoneOffset.UTC).withNano(123456789);
        lockEntity.setCreateTime(timeWithMillis);
        lockEntity.setUpdateTime(timeWithMillis);
        lockEntity.setExpiredTime(timeWithMillis.plusMinutes(5));

        boolean lockResult = lockRepository.tryLock(lockEntity);
        assertTrue(lockResult);

        // 获取锁信息并验证时间精度
        TaskLockEntity lockInfo = lockRepository.getLockInfo("test-task-7");
        assertNotNull(lockInfo);

        // 验证时间被正确存储和读取（可能会有精度损失，这是正常的）
        assertNotNull(lockInfo.getCreateTime());
        assertNotNull(lockInfo.getUpdateTime());
        assertNotNull(lockInfo.getExpiredTime());

        // 验证时间在合理范围内（允许毫秒级别的差异）
        assertTrue(Math.abs(lockInfo.getCreateTime().atZone(ZoneOffset.UTC).toInstant().toEpochMilli() -
                timeWithMillis.atZone(ZoneOffset.UTC).toInstant().toEpochMilli()) < 1000);
    }

    @Test
    public void testDatabaseCompatibility() throws SQLException {
        // 测试数据库类型检测
        DatabaseCompatibilityUtil.DatabaseType dbType =
                DatabaseCompatibilityUtil.detectDatabaseType(connection);
        assertEquals(DatabaseCompatibilityUtil.DatabaseType.H2, dbType);

        // 测试毫秒精度支持检测
        boolean supportsMillis = DatabaseCompatibilityUtil.supportsMillisecondPrecision(dbType);
        assertTrue(supportsMillis, "H2 should support millisecond precision");

        // 测试表存在性检查
        boolean tableExists = DatabaseCompatibilityUtil.tableExists(connection, "T_SCHEDULED_TASK_LOCK");
        assertTrue(tableExists, "Lock table should exist");
    }
}
