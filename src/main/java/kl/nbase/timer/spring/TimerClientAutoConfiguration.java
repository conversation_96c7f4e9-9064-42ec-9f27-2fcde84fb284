package kl.nbase.timer.spring;


import kl.nbase.timer.client.ITimerClient;
import kl.nbase.timer.client.TimerClientFactory;
import kl.nbase.timer.config.TimerConfig;
import kl.nbase.timer.job.ITimerJob;
import kl.nbase.timer.job.JobCollector;
import kl.nbase.timer.job.TimerJobFactory;
import kl.nbase.timer.lock.repository.LockRepositoryFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.util.CollectionUtils;

import javax.sql.DataSource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * Created on 2022/10/11 18:19
 */
@Configuration
@ConditionalOnBean(TimerConfig.class)
public class TimerClientAutoConfiguration {

    class SpringJobCollector implements JobCollector, ApplicationContextAware, InitializingBean {

        private ApplicationContext appContext;

        public SpringJobCollector(ApplicationContext applicationContext) {
            this.appContext = applicationContext;
        }

        @Override
        public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
            appContext = applicationContext;
        }

        @Override
        public void afterPropertiesSet() throws Exception {
            TimerJobFactory.collectJob(this);
        }

        @Override
        public List<ITimerJob> collect() {
            Map<String, ITimerJob> beansOfType = appContext.getBeansOfType(ITimerJob.class);
            if (!CollectionUtils.isEmpty(beansOfType)) {
                return new ArrayList<>(beansOfType.values());
            }
            return Collections.emptyList();
        }
    }

    @Bean(name = "springJobCollector")
    public SpringJobCollector springJobCollector(ApplicationContext applicationContext) {
        return new SpringJobCollector(applicationContext);
    }

    @Bean
    @DependsOn("springJobCollector")
    public ITimerClient timerClient(@Autowired(required = false) TimerConfig timerConfig, @Autowired(required = false) DataSource dataSource) {
        TimerClientFactory.init(timerConfig);
        LockRepositoryFactory.init(timerConfig, dataSource);
        ITimerClient client = TimerClientFactory.getClient();
        client.runAllJob();
        return client;
    }

}
