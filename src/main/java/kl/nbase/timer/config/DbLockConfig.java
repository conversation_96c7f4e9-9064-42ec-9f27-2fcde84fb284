package kl.nbase.timer.config;

import java.io.Serializable;
import java.time.ZoneId;
import java.time.ZoneOffset;

/**
 * @Author: guoq
 * @Date: 2023/10/27
 * @description: bd锁配置
 */
public class DbLockConfig implements Serializable {

    /**
     * 数据库url
     */
    private String dbUrl;

    private String username;

    private String password;

    /**
     * 是否使用UTC时间，默认false使用系统时区
     */
    private boolean useUtcTime = false;


    /**
     * 时间精度级别：MILLISECOND(毫秒), SECOND(秒)
     */
    private TimePrecision timePrecision = TimePrecision.MILLISECOND;

    /**
     * 是否启用过期锁自动清理
     */
    private boolean enableExpiredLockCleanup = true;

    /**
     * 过期锁清理间隔（分钟），默认30分钟
     */
    private int expiredLockCleanupIntervalMinutes = 30;

    /**
     * 锁超时时间（分钟），默认5分钟
     */
    private int lockTimeoutMinutes = 5;

    /**
     * 看门狗延期时间（分钟），默认4分钟
     */
    private int watchdogDelayMinutes = 4;

    public boolean isUseUtcTime() {
        return useUtcTime;
    }

    public void setUseUtcTime(boolean useUtcTime) {
        this.useUtcTime = useUtcTime;
    }


    /**
     * 获取时区对象
     */
    public ZoneId getTimeZone() {
        if (useUtcTime) {
            return ZoneOffset.UTC;
        }
        try {
            return ZoneId.systemDefault();
        } catch (Exception e) {
            // 如果时区ID无效，返回系统默认时区
            return ZoneId.systemDefault();
        }
    }

    public TimePrecision getTimePrecision() {
        return timePrecision;
    }

    public void setTimePrecision(TimePrecision timePrecision) {
        this.timePrecision = timePrecision;
    }

    public boolean isEnableExpiredLockCleanup() {
        return enableExpiredLockCleanup;
    }

    public void setEnableExpiredLockCleanup(boolean enableExpiredLockCleanup) {
        this.enableExpiredLockCleanup = enableExpiredLockCleanup;
    }

    public int getExpiredLockCleanupIntervalMinutes() {
        return expiredLockCleanupIntervalMinutes;
    }

    public void setExpiredLockCleanupIntervalMinutes(int expiredLockCleanupIntervalMinutes) {
        this.expiredLockCleanupIntervalMinutes = expiredLockCleanupIntervalMinutes;
    }

    public int getLockTimeoutMinutes() {
        return lockTimeoutMinutes;
    }

    public void setLockTimeoutMinutes(int lockTimeoutMinutes) {
        this.lockTimeoutMinutes = lockTimeoutMinutes;
    }

    public int getWatchdogDelayMinutes() {
        return watchdogDelayMinutes;
    }

    public void setWatchdogDelayMinutes(int watchdogDelayMinutes) {
        this.watchdogDelayMinutes = watchdogDelayMinutes;
    }

    /**
     * 时间精度枚举
     */
    public enum TimePrecision {
        /**
         * 毫秒精度
         */
        MILLISECOND,
        /**
         * 秒精度
         */
        SECOND
    }


    public String getDbUrl() {
        return dbUrl;
    }

    public void setDbUrl(String dbUrl) {
        this.dbUrl = dbUrl;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }
}
