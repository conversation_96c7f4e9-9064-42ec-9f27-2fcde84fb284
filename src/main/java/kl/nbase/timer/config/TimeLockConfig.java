package kl.nbase.timer.config;

import java.io.Serializable;
import java.time.ZoneId;
import java.time.ZoneOffset;

/**
 * @Author: guoq
 * @Date: 2023/12/07
 * @description: 分布式锁时间配置类，用于解决时区和精度问题
 */
public class TimeLockConfig implements Serializable {

    /**
     * 是否使用UTC时间，默认false使用系统时区
     */
    private boolean useUtcTime = false;
    
    /**
     * 时区ID，默认使用系统默认时区
     */
    private String timeZoneId = ZoneId.systemDefault().getId();
    
    /**
     * 时间精度级别：MILLISECOND(毫秒), SECOND(秒)
     */
    private TimePrecision timePrecision = TimePrecision.MILLISECOND;
    
    /**
     * 是否启用过期锁自动清理
     */
    private boolean enableExpiredLockCleanup = true;
    
    /**
     * 过期锁清理间隔（分钟），默认30分钟
     */
    private int expiredLockCleanupIntervalMinutes = 30;
    
    /**
     * 锁超时时间（分钟），默认5分钟
     */
    private int lockTimeoutMinutes = 5;
    
    /**
     * 看门狗延期时间（分钟），默认4分钟
     */
    private int watchdogDelayMinutes = 4;

    public boolean isUseUtcTime() {
        return useUtcTime;
    }

    public void setUseUtcTime(boolean useUtcTime) {
        this.useUtcTime = useUtcTime;
    }

    public String getTimeZoneId() {
        return timeZoneId;
    }

    public void setTimeZoneId(String timeZoneId) {
        this.timeZoneId = timeZoneId;
    }
    
    /**
     * 获取时区对象
     */
    public ZoneId getTimeZone() {
        if (useUtcTime) {
            return ZoneOffset.UTC;
        }
        try {
            return ZoneId.of(timeZoneId);
        } catch (Exception e) {
            // 如果时区ID无效，返回系统默认时区
            return ZoneId.systemDefault();
        }
    }

    public TimePrecision getTimePrecision() {
        return timePrecision;
    }

    public void setTimePrecision(TimePrecision timePrecision) {
        this.timePrecision = timePrecision;
    }

    public boolean isEnableExpiredLockCleanup() {
        return enableExpiredLockCleanup;
    }

    public void setEnableExpiredLockCleanup(boolean enableExpiredLockCleanup) {
        this.enableExpiredLockCleanup = enableExpiredLockCleanup;
    }

    public int getExpiredLockCleanupIntervalMinutes() {
        return expiredLockCleanupIntervalMinutes;
    }

    public void setExpiredLockCleanupIntervalMinutes(int expiredLockCleanupIntervalMinutes) {
        this.expiredLockCleanupIntervalMinutes = expiredLockCleanupIntervalMinutes;
    }

    public int getLockTimeoutMinutes() {
        return lockTimeoutMinutes;
    }

    public void setLockTimeoutMinutes(int lockTimeoutMinutes) {
        this.lockTimeoutMinutes = lockTimeoutMinutes;
    }

    public int getWatchdogDelayMinutes() {
        return watchdogDelayMinutes;
    }

    public void setWatchdogDelayMinutes(int watchdogDelayMinutes) {
        this.watchdogDelayMinutes = watchdogDelayMinutes;
    }

    /**
     * 时间精度枚举
     */
    public enum TimePrecision {
        /**
         * 毫秒精度
         */
        MILLISECOND,
        /**
         * 秒精度
         */
        SECOND
    }
    
    /**
     * 创建默认配置
     */
    public static TimeLockConfig createDefault() {
        return new TimeLockConfig();
    }
    
    /**
     * 创建UTC时间配置
     */
    public static TimeLockConfig createUtcConfig() {
        TimeLockConfig config = new TimeLockConfig();
        config.setUseUtcTime(true);
        return config;
    }
    
    /**
     * 创建指定时区配置
     */
    public static TimeLockConfig createWithTimeZone(String timeZoneId) {
        TimeLockConfig config = new TimeLockConfig();
        config.setTimeZoneId(timeZoneId);
        return config;
    }
    
    /**
     * 创建秒精度配置（适用于不支持毫秒精度的数据库）
     */
    public static TimeLockConfig createSecondPrecisionConfig() {
        TimeLockConfig config = new TimeLockConfig();
        config.setTimePrecision(TimePrecision.SECOND);
        return config;
    }

    @Override
    public String toString() {
        return "TimeLockConfig{" +
                "useUtcTime=" + useUtcTime +
                ", timeZoneId='" + timeZoneId + '\'' +
                ", timePrecision=" + timePrecision +
                ", enableExpiredLockCleanup=" + enableExpiredLockCleanup +
                ", expiredLockCleanupIntervalMinutes=" + expiredLockCleanupIntervalMinutes +
                ", lockTimeoutMinutes=" + lockTimeoutMinutes +
                ", watchdogDelayMinutes=" + watchdogDelayMinutes +
                '}';
    }
}
