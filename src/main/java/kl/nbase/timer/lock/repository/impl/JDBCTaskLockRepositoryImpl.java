package kl.nbase.timer.lock.repository.impl;

import kl.nbase.timer.config.DbLockConfig;
import kl.nbase.timer.config.TimerConfig;
import kl.nbase.timer.lock.entity.TaskLockEntity;
import kl.nbase.timer.lock.repository.ITaskLockRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.sql.DataSource;
import java.sql.*;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;

/**
 * @Author: guoq
 * @Date: 2023/10/25
 * @description: jdbc 任务锁实现
 */
public class JDBCTaskLockRepositoryImpl implements ITaskLockRepository {

    private static final Logger log = LoggerFactory.getLogger(JDBCTaskLockRepositoryImpl.class);

    // 数据库字段名常量
    private static final String TASK_ID = "TASK_ID";
    private static final String TASK_NAME = "TASK_NAME";
    private static final String SERVICE_ID = "SERVICE_ID";
    private static final String VERSION = "VERSION";
    private static final String EXPIRED_TIME = "EXPIRED_TIME";
    private static final String CREATE_TIME = "CREATE_TIME";
    private static final String UPDATE_TIME = "UPDATE_TIME";

    // SQL语句常量 - 使用参数化查询避免SQL注入
    private static final String QUERY_SQL = "SELECT TASK_ID,TASK_NAME,SERVICE_ID,VERSION,EXPIRED_TIME,CREATE_TIME," + "UPDATE_TIME FROM T_SCHEDULED_TASK_LOCK WHERE TASK_ID=? ";
    private static final String INSERT_SQL = "INSERT INTO T_SCHEDULED_TASK_LOCK(TASK_ID,TASK_NAME,SERVICE_ID," + "VERSION, EXPIRED_TIME, CREATE_TIME,UPDATE_TIME) VALUES(?,?,?,?,?,?,?)";
    private static final String UPDATE_SQL = "UPDATE T_SCHEDULED_TASK_LOCK SET EXPIRED_TIME=?, UPDATE_TIME=?, VERSION=? " + "WHERE TASK_ID=? AND SERVICE_ID=? ";
    private static final String DELETE_SQL = "DELETE FROM T_SCHEDULED_TASK_LOCK WHERE TASK_ID=? ";
    private static final String DELETE_WITH_SERVICE_SQL = "DELETE FROM T_SCHEDULED_TASK_LOCK WHERE TASK_ID=? AND SERVICE_ID=?";

    // 清理过期锁的SQL
    private static final String CLEANUP_EXPIRED_SQL = "DELETE FROM T_SCHEDULED_TASK_LOCK WHERE EXPIRED_TIME < ?";

    // 数据库连接配置
    private final String url;
    private final String user;
    private final String password;
    private final DataSource dataSource;
    private final boolean useUtcTime;


    public JDBCTaskLockRepositoryImpl(TimerConfig timerConfig, DataSource dataSource) {
        this.useUtcTime = timerConfig.getDbLock().isUseUtcTime();

        if (dataSource != null) {
            this.dataSource = dataSource;
            this.url = null;
            this.user = null;
            this.password = null;
        } else {
            this.dataSource = null;
            DbLockConfig dbLock = timerConfig.getDbLock();
            this.url = dbLock.getDbUrl();
            this.user = dbLock.getUsername();
            this.password = dbLock.getPassword();
        }
    }

    public Connection getConnection() throws SQLException {
        if (dataSource != null) {
            return dataSource.getConnection();
        } else {
            return DriverManager.getConnection(url, user, password);
        }
    }

    @Override
    public boolean tryLock(TaskLockEntity lockEntity) {
        try (Connection connection = getConnection(); PreparedStatement preparedStatement = connection.prepareStatement(INSERT_SQL)) {

            preparedStatement.setString(1, lockEntity.getTaskId());
            preparedStatement.setString(2, lockEntity.getTaskName());
            preparedStatement.setString(3, lockEntity.getServiceId());
            preparedStatement.setInt(4, lockEntity.getVersion());
            preparedStatement.setTimestamp(5, toTimestamp(lockEntity.getExpiredTime()));
            preparedStatement.setTimestamp(6, toTimestamp(lockEntity.getCreateTime()));
            preparedStatement.setTimestamp(7, toTimestamp(lockEntity.getUpdateTime()));

            int result = preparedStatement.executeUpdate();
            return result > 0;

        } catch (SQLIntegrityConstraintViolationException e1) {
            log.debug("Job {} try jdbc lock failure due to constraint violation!", lockEntity.getTaskId());
            return false;
        } catch (SQLException e) {
            // 检查是否是主键冲突或唯一约束冲突
            if (isDuplicateKeyException(e)) {
                log.debug("Job {} try jdbc lock failure due to duplicate key!", lockEntity.getTaskId());
                return false;
            }
            log.error("Job {} try jdbc lock error!", lockEntity.getTaskId(), e);
            return false;
        } catch (Exception e) {
            log.error("Job {} try jdbc lock error!", lockEntity.getTaskId(), e);
            return false;
        }
    }

    @Override
    public boolean delayLock(TaskLockEntity lockEntity) {
        try (Connection connection = getConnection(); PreparedStatement preparedStatement = connection.prepareStatement(UPDATE_SQL)) {

            preparedStatement.setTimestamp(1, toTimestamp(lockEntity.getExpiredTime()));
            preparedStatement.setTimestamp(2, toTimestamp(lockEntity.getUpdateTime()));
            preparedStatement.setInt(3, lockEntity.getVersion());
            preparedStatement.setString(4, lockEntity.getTaskId());
            preparedStatement.setString(5, lockEntity.getServiceId());

            int result = preparedStatement.executeUpdate();
            if (result > 0) {
                log.info("Task {} delay successful, version {} of delayed locks!", lockEntity.getTaskId(), lockEntity.getVersion());
                return true;
            } else {
                log.warn("Task {} delay failed, no rows affected!", lockEntity.getTaskId());
                return false;
            }
        } catch (Exception e) {
            log.error("Job {} delay jdbc lock failure!", lockEntity.getTaskId(), e);
            return false;
        }
    }

    @Override
    public TaskLockEntity getLockInfo(String taskId) {
        try (Connection connection = getConnection(); PreparedStatement preparedStatement = connection.prepareStatement(QUERY_SQL)) {

            preparedStatement.setString(1, taskId);

            try (ResultSet resultSet = preparedStatement.executeQuery()) {
                if (resultSet.next()) {
                    TaskLockEntity taskLockEntity = new TaskLockEntity();
                    taskLockEntity.setTaskId(resultSet.getString(TASK_ID));
                    taskLockEntity.setTaskName(resultSet.getString(TASK_NAME));
                    taskLockEntity.setServiceId(resultSet.getString(SERVICE_ID));
                    taskLockEntity.setVersion(resultSet.getInt(VERSION));
                    taskLockEntity.setExpiredTime(toLocalDateTime(resultSet.getTimestamp(EXPIRED_TIME)));
                    taskLockEntity.setCreateTime(toLocalDateTime(resultSet.getTimestamp(CREATE_TIME)));
                    taskLockEntity.setUpdateTime(toLocalDateTime(resultSet.getTimestamp(UPDATE_TIME)));
                    return taskLockEntity;
                }
            }
        } catch (Exception e) {
            log.error("Job {} get jdbc lock info failure!", taskId, e);
        }
        return null;
    }

    @Override
    public boolean unlock(String taskId) {
        try (Connection connection = getConnection(); PreparedStatement preparedStatement = connection.prepareStatement(DELETE_SQL)) {

            preparedStatement.setString(1, taskId);
            int result = preparedStatement.executeUpdate();
            return result > 0;

        } catch (Exception e) {
            log.error("Job {} release jdbc lock info failure!", taskId, e);
            return false;
        }
    }

    @Override
    public boolean unlock(String taskId, String serviceId) {
        try (Connection connection = getConnection(); PreparedStatement preparedStatement = connection.prepareStatement(DELETE_WITH_SERVICE_SQL)) {

            preparedStatement.setString(1, taskId);
            preparedStatement.setString(2, serviceId);
            int result = preparedStatement.executeUpdate();
            return result > 0;

        } catch (Exception e) {
            log.error("Job {} release jdbc lock info failure!", taskId, e);
            return false;
        }
    }

    /**
     * 清理过期的锁记录
     *
     * @return 清理的记录数
     */
    public int cleanupExpiredLocks() {
        try (Connection connection = getConnection(); PreparedStatement preparedStatement = connection.prepareStatement(CLEANUP_EXPIRED_SQL)) {

            preparedStatement.setTimestamp(1, toTimestamp(getCurrentTime()));
            int result = preparedStatement.executeUpdate();
            if (result > 0) {
                log.info("Cleaned up {} expired lock records", result);
            }
            return result;

        } catch (Exception e) {
            log.error("Failed to cleanup expired locks!", e);
            return 0;
        }
    }

    /**
     * 将LocalDateTime转换为Timestamp，处理时区和精度问题
     */
    private Timestamp toTimestamp(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return null;
        }

        Instant instant;
        if (useUtcTime) {
            // 如果使用UTC时间，直接转换
            instant = localDateTime.atZone(ZoneOffset.UTC).toInstant();
        } else {
            // 使用配置的时区
            instant = localDateTime.atZone(ZoneId.systemDefault()).toInstant();
        }

        // 截断到毫秒精度，避免纳秒精度导致的数据库兼容性问题
        long epochMilli = instant.toEpochMilli();
        return new Timestamp(epochMilli);
    }

    /**
     * 将数据库Timestamp转换为LocalDateTime
     */
    private LocalDateTime toLocalDateTime(Timestamp timestamp) {
        if (timestamp == null) {
            return null;
        }

        if (useUtcTime) {
            // 如果使用UTC时间，转换为UTC的LocalDateTime
            return timestamp.toInstant().atZone(ZoneOffset.UTC).toLocalDateTime();
        } else {
            // 使用配置的时区
            return timestamp.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        }
    }

    /**
     * 获取当前时间
     */
    private LocalDateTime getCurrentTime() {
        if (useUtcTime) {
            return LocalDateTime.now(ZoneOffset.UTC);
        } else {
            return LocalDateTime.now();
        }
    }

    /**
     * 检查是否是重复键异常
     */
    private boolean isDuplicateKeyException(SQLException e) {
        String sqlState = e.getSQLState();
        int errorCode = e.getErrorCode();

        // MySQL: 1062, PostgreSQL: 23505, Oracle: 1, SQL Server: 2627
        return "23000".equals(sqlState) || "23505".equals(sqlState) || errorCode == 1062 || errorCode == 23505 || errorCode == 1 || errorCode == 2627;
    }
}
