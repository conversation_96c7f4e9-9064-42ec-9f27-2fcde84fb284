package kl.nbase.timer.lock.entity;

import kl.nbase.timer.lock.TimerContext;
import kl.nbase.timer.lock.WatchDog;
import kl.nbase.timer.lock.repository.ITaskLockRepository;
import kl.nbase.timer.lock.repository.LockRepositoryFactory;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * @Author: guoq
 * @Date: 2023/10/25
 * @description: 锁实体
 */
public class TaskLockEntity implements Serializable {
    private static final Logger log = LoggerFactory.getLogger(TaskLockEntity.class);
    /**
     * 分布式定时任务默认执行时间 5分钟
     */
    private static final Integer TASK_EXTENSION_TIME_MINUTES = 5;
    /**
     * watchdog 延期时间，首次默认为4分钟
     */
    private static final Integer LOCK_DELAY_TIME_MILLISECOND = 4;

    /**
     * 分钟
     */
    private static final long MINUTES_MILLISECOND = 60 * 1000L;
    /**
     * 唯一标识
     */
    private String taskId;

    private String taskName;

    private String serviceId;

    /**
     * 过期时间
     */
    private LocalDateTime expiredTime;

    private int version;


    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public TaskLockEntity() {

    }

    public TaskLockEntity(String taskId) {
        this(taskId, null);
    }

    public TaskLockEntity(String taskId, String taskName) {
        this.taskId = taskId;
        this.taskName = taskName;
        this.version = 0;
        this.serviceId = TimerContext.getServiceId();
        // 使用当前时间，确保时区一致性
        LocalDateTime now = getCurrentTime();
        this.createTime = now;
        this.expiredTime = now.plusMinutes(TASK_EXTENSION_TIME_MINUTES);
        this.updateTime = now;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getServiceId() {
        return serviceId;
    }

    public void setServiceId(String serviceId) {
        this.serviceId = serviceId;
    }

    public LocalDateTime getExpiredTime() {
        return expiredTime;
    }

    public void setExpiredTime(LocalDateTime expiredTime) {
        this.expiredTime = expiredTime;
    }

    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 尝试加锁
     *
     * @return
     */
    public boolean tryLock() {
        try {
            do {
                ITaskLockRepository taskLockRepository = LockRepositoryFactory.getTaskLockRepository();
                if (taskLockRepository.tryLock(this)) {

                    WatchDog.addTask(this, LOCK_DELAY_TIME_MILLISECOND * MINUTES_MILLISECOND);
                    return true;
                }
                // 获取锁失败，获取锁信息
                TaskLockEntity lockInfo = taskLockRepository.getLockInfo(getTaskId());
                // 获取锁的业务已经执行完毕并且释放锁，直接跳出即可
                if(ObjectUtils.isEmpty(lockInfo)){
                    return false;
                }
                // 查询锁是否过期，使用统一的时间获取方法
                if (lockInfo.getExpiredTime().isAfter(getCurrentTime())) {
                    // 未过期
                    return false;
                }
                taskLockRepository.unlock(getTaskId());

            } while (true);
        } catch (Exception e) {
            log.error("Attempt to lock failure!", e);
        }
        return false;
    }


    /**
     * 延期锁
     *
     * @return 延期结果
     */
    public boolean delayLock() {
        setExpiredTime(getExpiredTime().plusMinutes(TASK_EXTENSION_TIME_MINUTES));
        setVersion(version + 1);
        setUpdateTime(getCurrentTime());
        boolean result = LockRepositoryFactory.getTaskLockRepository().delayLock(this);
        if (result) {
            WatchDog.addTask(this, TASK_EXTENSION_TIME_MINUTES * MINUTES_MILLISECOND);
        }
        return result;
    }

    public boolean unLock() {
        boolean result = LockRepositoryFactory.getTaskLockRepository().unlock(getTaskId(), getServiceId());
        if (result) {
            WatchDog.removeTask(this);
        }
        return result;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        TaskLockEntity that = (TaskLockEntity) o;

        if (version != that.version) return false;
        if (!Objects.equals(taskId, that.taskId)) return false;
        if (!Objects.equals(taskName, that.taskName)) return false;
        if (!Objects.equals(serviceId, that.serviceId)) return false;
        return Objects.equals(createTime, that.createTime);
    }

    @Override
    public int hashCode() {
        int result = taskId != null ? taskId.hashCode() : 0;
        result = 31 * result + (taskName != null ? taskName.hashCode() : 0);
        result = 31 * result + (serviceId != null ? serviceId.hashCode() : 0);
        result = 31 * result + version;
        result = 31 * result + (createTime != null ? createTime.hashCode() : 0);
        return result;
    }

    /**
     * 获取当前时间，确保时区一致性
     * 可以根据需要配置为使用UTC时间或系统默认时区
     *
     * @return 当前时间
     */
    private static LocalDateTime getCurrentTime() {
        // 使用系统默认时区的当前时间
        // 如果需要更严格的时区控制，可以使用 ZonedDateTime.now(ZoneOffset.UTC).toLocalDateTime()
        return LocalDateTime.now();
    }
}
