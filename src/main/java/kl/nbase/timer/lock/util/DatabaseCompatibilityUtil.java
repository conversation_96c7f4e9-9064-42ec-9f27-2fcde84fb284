package kl.nbase.timer.lock.util;

import kl.nbase.timer.config.DbLockConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;

/**
 * @Author: guoq
 * @Date: 2023/12/07
 * @description: 数据库兼容性工具类，处理不同数据库的时间精度和类型差异
 */
public class DatabaseCompatibilityUtil {

    private static final Logger log = LoggerFactory.getLogger(DatabaseCompatibilityUtil.class);

    /**
     * 数据库类型枚举
     */
    public enum DatabaseType {
        MYSQL("MySQL"),
        POSTGRESQL("PostgreSQL"),
        ORACLE("Oracle"),
        SQL_SERVER("Microsoft SQL Server"),
        H2("H2"),
        SQLITE("SQLite"),
        UNKNOWN("Unknown");

        private final String productName;

        DatabaseType(String productName) {
            this.productName = productName;
        }

        public String getProductName() {
            return productName;
        }
    }

    /**
     * 检测数据库类型
     */
    public static DatabaseType detectDatabaseType(Connection connection) {
        try {
            DatabaseMetaData metaData = connection.getMetaData();
            String productName = metaData.getDatabaseProductName();
            
            if (productName.toLowerCase().contains("mysql")) {
                return DatabaseType.MYSQL;
            } else if (productName.toLowerCase().contains("postgresql")) {
                return DatabaseType.POSTGRESQL;
            } else if (productName.toLowerCase().contains("oracle")) {
                return DatabaseType.ORACLE;
            } else if (productName.toLowerCase().contains("microsoft") || 
                       productName.toLowerCase().contains("sql server")) {
                return DatabaseType.SQL_SERVER;
            } else if (productName.toLowerCase().contains("h2")) {
                return DatabaseType.H2;
            } else if (productName.toLowerCase().contains("sqlite")) {
                return DatabaseType.SQLITE;
            } else {
                log.warn("Unknown database type: {}", productName);
                return DatabaseType.UNKNOWN;
            }
        } catch (SQLException e) {
            log.error("Failed to detect database type", e);
            return DatabaseType.UNKNOWN;
        }
    }

    /**
     * 检查数据库是否支持毫秒精度
     */
    public static boolean supportsMillisecondPrecision(DatabaseType databaseType) {
        switch (databaseType) {
            case MYSQL:
                // MySQL 5.6.4+ 支持毫秒精度
                return true;
            case POSTGRESQL:
                // PostgreSQL 支持微秒精度
                return true;
            case ORACLE:
                // Oracle 支持毫秒精度
                return true;
            case SQL_SERVER:
                // SQL Server 2008+ 支持毫秒精度
                return true;
            case H2:
                // H2 支持纳秒精度
                return true;
            case SQLITE:
                // SQLite 的 DATETIME 类型精度取决于存储格式
                return false;
            default:
                return false;
        }
    }

    /**
     * 根据数据库类型和配置转换LocalDateTime为Timestamp
     */
    public static Timestamp toTimestamp(LocalDateTime localDateTime, DbLockConfig config, DatabaseType databaseType) {
        if (localDateTime == null) {
            return null;
        }

        Instant instant;
        if (config.isUseUtcTime()) {
            instant = localDateTime.atZone(ZoneOffset.UTC).toInstant();
        } else {
            instant = localDateTime.atZone(config.getTimeZone()).toInstant();
        }

        // 根据配置和数据库类型调整精度
        if (config.getTimePrecision() == DbLockConfig.TimePrecision.SECOND || 
            !supportsMillisecondPrecision(databaseType)) {
            // 截断到秒精度
            long epochSecond = instant.getEpochSecond();
            return new Timestamp(epochSecond * 1000);
        } else {
            // 使用毫秒精度
            long epochMilli = instant.toEpochMilli();
            return new Timestamp(epochMilli);
        }
    }

    /**
     * 根据数据库类型和配置转换Timestamp为LocalDateTime
     */
    public static LocalDateTime toLocalDateTime(Timestamp timestamp, DbLockConfig config) {
        if (timestamp == null) {
            return null;
        }

        if (config.isUseUtcTime()) {
            return timestamp.toInstant().atZone(ZoneOffset.UTC).toLocalDateTime();
        } else {
            return timestamp.toInstant().atZone(config.getTimeZone()).toLocalDateTime();
        }
    }

    /**
     * 获取当前时间
     */
    public static LocalDateTime getCurrentTime(DbLockConfig config) {
        if (config.isUseUtcTime()) {
            return LocalDateTime.now(ZoneOffset.UTC);
        } else {
            return LocalDateTime.now(config.getTimeZone());
        }
    }

    /**
     * 检查是否是重复键异常
     */
    public static boolean isDuplicateKeyException(SQLException e, DatabaseType databaseType) {
        String sqlState = e.getSQLState();
        int errorCode = e.getErrorCode();

        switch (databaseType) {
            case MYSQL:
                return "23000".equals(sqlState) && errorCode == 1062;
            case POSTGRESQL:
                return "23505".equals(sqlState);
            case ORACLE:
                return errorCode == 1; // ORA-00001: unique constraint violated
            case SQL_SERVER:
                return errorCode == 2627 || errorCode == 2601;
            case H2:
                return "23505".equals(sqlState);
            case SQLITE:
                return "23000".equals(sqlState) || errorCode == 19; // SQLITE_CONSTRAINT
            default:
                // 通用检查
                return "23000".equals(sqlState) || "23505".equals(sqlState);
        }
    }

    /**
     * 获取数据库特定的建表SQL
     */
    public static String getCreateTableSql(DatabaseType databaseType) {
        switch (databaseType) {
            case MYSQL:
                return "CREATE TABLE IF NOT EXISTS T_SCHEDULED_TASK_LOCK (" +
                       "TASK_ID VARCHAR(255) NOT NULL PRIMARY KEY, " +
                       "TASK_NAME VARCHAR(255), " +
                       "SERVICE_ID VARCHAR(255) NOT NULL, " +
                       "VERSION INT NOT NULL DEFAULT 0, " +
                       "EXPIRED_TIME TIMESTAMP(3) NOT NULL, " +
                       "CREATE_TIME TIMESTAMP(3) NOT NULL, " +
                       "UPDATE_TIME TIMESTAMP(3) NOT NULL, " +
                       "INDEX idx_expired_time (EXPIRED_TIME)" +
                       ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

            case POSTGRESQL:
                return "CREATE TABLE IF NOT EXISTS T_SCHEDULED_TASK_LOCK (" +
                       "TASK_ID VARCHAR(255) NOT NULL PRIMARY KEY, " +
                       "TASK_NAME VARCHAR(255), " +
                       "SERVICE_ID VARCHAR(255) NOT NULL, " +
                       "VERSION INTEGER NOT NULL DEFAULT 0, " +
                       "EXPIRED_TIME TIMESTAMP(3) NOT NULL, " +
                       "CREATE_TIME TIMESTAMP(3) NOT NULL, " +
                       "UPDATE_TIME TIMESTAMP(3) NOT NULL" +
                       "); " +
                       "CREATE INDEX IF NOT EXISTS idx_expired_time ON T_SCHEDULED_TASK_LOCK (EXPIRED_TIME)";

            case ORACLE:
                return "CREATE TABLE T_SCHEDULED_TASK_LOCK (" +
                       "TASK_ID VARCHAR2(255) NOT NULL PRIMARY KEY, " +
                       "TASK_NAME VARCHAR2(255), " +
                       "SERVICE_ID VARCHAR2(255) NOT NULL, " +
                       "VERSION NUMBER(10) DEFAULT 0 NOT NULL, " +
                       "EXPIRED_TIME TIMESTAMP(3) NOT NULL, " +
                       "CREATE_TIME TIMESTAMP(3) NOT NULL, " +
                       "UPDATE_TIME TIMESTAMP(3) NOT NULL" +
                       ")";

            case SQL_SERVER:
                return "IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='T_SCHEDULED_TASK_LOCK' AND xtype='U') " +
                       "CREATE TABLE T_SCHEDULED_TASK_LOCK (" +
                       "TASK_ID NVARCHAR(255) NOT NULL PRIMARY KEY, " +
                       "TASK_NAME NVARCHAR(255), " +
                       "SERVICE_ID NVARCHAR(255) NOT NULL, " +
                       "VERSION INT NOT NULL DEFAULT 0, " +
                       "EXPIRED_TIME DATETIME2(3) NOT NULL, " +
                       "CREATE_TIME DATETIME2(3) NOT NULL, " +
                       "UPDATE_TIME DATETIME2(3) NOT NULL" +
                       ")";

            case H2:
                return "CREATE TABLE IF NOT EXISTS T_SCHEDULED_TASK_LOCK (" +
                       "TASK_ID VARCHAR(255) NOT NULL PRIMARY KEY, " +
                       "TASK_NAME VARCHAR(255), " +
                       "SERVICE_ID VARCHAR(255) NOT NULL, " +
                       "VERSION INT NOT NULL DEFAULT 0, " +
                       "EXPIRED_TIME TIMESTAMP NOT NULL, " +
                       "CREATE_TIME TIMESTAMP NOT NULL, " +
                       "UPDATE_TIME TIMESTAMP NOT NULL" +
                       ")";

            case SQLITE:
                return "CREATE TABLE IF NOT EXISTS T_SCHEDULED_TASK_LOCK (" +
                       "TASK_ID TEXT NOT NULL PRIMARY KEY, " +
                       "TASK_NAME TEXT, " +
                       "SERVICE_ID TEXT NOT NULL, " +
                       "VERSION INTEGER NOT NULL DEFAULT 0, " +
                       "EXPIRED_TIME TEXT NOT NULL, " +
                       "CREATE_TIME TEXT NOT NULL, " +
                       "UPDATE_TIME TEXT NOT NULL" +
                       ")";

            default:
                // 通用SQL
                return "CREATE TABLE T_SCHEDULED_TASK_LOCK (" +
                       "TASK_ID VARCHAR(255) NOT NULL PRIMARY KEY, " +
                       "TASK_NAME VARCHAR(255), " +
                       "SERVICE_ID VARCHAR(255) NOT NULL, " +
                       "VERSION INTEGER NOT NULL DEFAULT 0, " +
                       "EXPIRED_TIME TIMESTAMP NOT NULL, " +
                       "CREATE_TIME TIMESTAMP NOT NULL, " +
                       "UPDATE_TIME TIMESTAMP NOT NULL" +
                       ")";
        }
    }

    /**
     * 检查表是否存在
     */
    public static boolean tableExists(Connection connection, String tableName) {
        try {
            DatabaseMetaData metaData = connection.getMetaData();
            try (ResultSet tables = metaData.getTables(null, null, tableName, new String[]{"TABLE"})) {
                return tables.next();
            }
        } catch (SQLException e) {
            log.error("Failed to check if table exists: {}", tableName, e);
            return false;
        }
    }

    /**
     * 自动创建锁表
     */
    public static boolean createLockTableIfNotExists(Connection connection) {
        try {
            if (!tableExists(connection, "T_SCHEDULED_TASK_LOCK")) {
                DatabaseType databaseType = detectDatabaseType(connection);
                String createSql = getCreateTableSql(databaseType);
                
                try (Statement statement = connection.createStatement()) {
                    statement.execute(createSql);
                    log.info("Created lock table for database type: {}", databaseType);
                    return true;
                }
            }
            return true;
        } catch (SQLException e) {
            log.error("Failed to create lock table", e);
            return false;
        }
    }
}
