# 分布式锁优化方案指南

## 问题背景

原有的分布式锁实现存在以下问题：

1. **时间精度问题**：
   - 使用`Timestamp.valueOf(LocalDateTime)`可能导致纳秒精度丢失
   - 不同数据库对时间戳精度支持不一致（如超过9位纳秒）
   - 可能引发插入失败或精度丢失

2. **时区转换问题**：
   - `LocalDateTime.now()`使用系统默认时区
   - 数据库时间戳可能与应用服务器时区不一致
   - 分布式环境中不同节点时区可能不同，导致分布式锁失效

3. **数据库兼容性问题**：
   - SQL语句使用字符串格式化存在SQL注入风险
   - 不同数据库的时间戳类型和精度支持不同

## 优化方案

### 1. 增强版JDBC锁实现

创建了`EnhancedJDBCTaskLockRepositoryImpl`类，提供以下改进：

#### 时间精度处理
```java
// 将LocalDateTime转换为毫秒精度的Timestamp，避免纳秒精度问题
private Timestamp toTimestamp(LocalDateTime localDateTime) {
    if (localDateTime == null) return null;
    
    Instant instant = localDateTime.atZone(timeZone).toInstant();
    // 截断到毫秒精度，避免纳秒精度导致的数据库兼容性问题
    long epochMilli = instant.toEpochMilli();
    return new Timestamp(epochMilli);
}
```

#### 时区一致性
```java
// 支持UTC时间和自定义时区
private final ZoneId timeZone;
private final boolean useUtcTime;

// 获取当前时间，确保时区一致性
private LocalDateTime getCurrentTime() {
    if (useUtcTime) {
        return LocalDateTime.now(ZoneOffset.UTC);
    } else {
        return LocalDateTime.now(timeZone);
    }
}
```

#### SQL注入防护
```java
// 使用参数化查询替代字符串格式化
private static final String QUERY_SQL = 
    "SELECT TASK_ID,TASK_NAME,SERVICE_ID,VERSION,EXPIRED_TIME,CREATE_TIME,UPDATE_TIME " +
    "FROM T_SCHEDULED_TASK_LOCK WHERE TASK_ID=? ";

// 在查询时使用参数绑定
preparedStatement.setString(1, taskId);
```

### 2. 时间锁配置类

创建了`TimeLockConfig`类，提供灵活的时间配置：

```java
public class TimeLockConfig {
    private boolean useUtcTime = false;                    // 是否使用UTC时间
    private String timeZoneId = ZoneId.systemDefault().getId(); // 时区ID
    private TimePrecision timePrecision = TimePrecision.MILLISECOND; // 时间精度
    private boolean enableExpiredLockCleanup = true;       // 启用过期锁清理
    private int expiredLockCleanupIntervalMinutes = 30;    // 清理间隔
    private int lockTimeoutMinutes = 5;                    // 锁超时时间
    private int watchdogDelayMinutes = 4;                  // 看门狗延期时间
}
```

### 3. 数据库兼容性工具

创建了`DatabaseCompatibilityUtil`类，处理不同数据库的差异：

#### 数据库类型检测
```java
public static DatabaseType detectDatabaseType(Connection connection) {
    DatabaseMetaData metaData = connection.getMetaData();
    String productName = metaData.getDatabaseProductName();
    // 根据产品名称识别数据库类型
}
```

#### 精度支持检测
```java
public static boolean supportsMillisecondPrecision(DatabaseType databaseType) {
    switch (databaseType) {
        case MYSQL:    return true;  // MySQL 5.6.4+ 支持毫秒精度
        case POSTGRESQL: return true; // PostgreSQL 支持微秒精度
        case ORACLE:   return true;  // Oracle 支持毫秒精度
        // ...
    }
}
```

#### 自动建表
```java
public static String getCreateTableSql(DatabaseType databaseType) {
    switch (databaseType) {
        case MYSQL:
            return "CREATE TABLE IF NOT EXISTS T_SCHEDULED_TASK_LOCK (" +
                   "TASK_ID VARCHAR(255) NOT NULL PRIMARY KEY, " +
                   "EXPIRED_TIME TIMESTAMP(3) NOT NULL, " + // 毫秒精度
                   "...)";
        // 其他数据库的建表语句...
    }
}
```

## 使用方法

### 1. 基本配置

```java
// 创建TimerConfig
TimerConfig timerConfig = new TimerConfig();
timerConfig.setDistributedEnabled(true);

// 配置数据库锁
DbLockConfig dbLockConfig = new DbLockConfig();
dbLockConfig.setDbUrl("********************************");
dbLockConfig.setUsername("user");
dbLockConfig.setPassword("password");
timerConfig.setDbLock(dbLockConfig);

// 配置时间锁（可选）
TimeLockConfig timeLockConfig = new TimeLockConfig();
timeLockConfig.setUseUtcTime(true); // 使用UTC时间确保一致性
timeLockConfig.setTimePrecision(TimeLockConfig.TimePrecision.MILLISECOND);
timerConfig.setTimeLock(timeLockConfig);
```

### 2. 使用增强版锁实现

```java
// 创建增强版锁仓库
EnhancedJDBCTaskLockRepositoryImpl lockRepository = 
    new EnhancedJDBCTaskLockRepositoryImpl(timerConfig, dataSource, 
                                          ZoneOffset.UTC, true);

// 使用锁
TaskLockEntity lockEntity = new TaskLockEntity("task-id", "task-name");
if (lockRepository.tryLock(lockEntity)) {
    try {
        // 执行业务逻辑
        doBusinessLogic();
    } finally {
        lockRepository.unlock(lockEntity.getTaskId(), lockEntity.getServiceId());
    }
}
```

### 3. 不同场景的配置建议

#### 场景1：单时区环境
```java
TimeLockConfig config = TimeLockConfig.createDefault();
// 使用系统默认时区，适合单机房部署
```

#### 场景2：多时区分布式环境
```java
TimeLockConfig config = TimeLockConfig.createUtcConfig();
// 使用UTC时间，确保不同时区节点的时间一致性
```

#### 场景3：数据库精度限制
```java
TimeLockConfig config = TimeLockConfig.createSecondPrecisionConfig();
// 使用秒精度，适用于不支持毫秒精度的老版本数据库
```

#### 场景4：自定义时区
```java
TimeLockConfig config = TimeLockConfig.createWithTimeZone("Asia/Shanghai");
// 使用指定时区
```

## 迁移指南

### 从原有实现迁移

1. **替换实现类**：
   ```java
   // 原有实现
   ITaskLockRepository repository = new JDBCTaskLockRepositoryImpl(timerConfig, dataSource);
   
   // 新实现
   ITaskLockRepository repository = new EnhancedJDBCTaskLockRepositoryImpl(
       timerConfig, dataSource, ZoneOffset.UTC, true);
   ```

2. **更新配置**：
   ```java
   // 添加时间锁配置
   TimeLockConfig timeLockConfig = TimeLockConfig.createUtcConfig();
   timerConfig.setTimeLock(timeLockConfig);
   ```

3. **数据库表更新**（如需要）：
   ```sql
   -- MySQL示例：添加毫秒精度支持
   ALTER TABLE T_SCHEDULED_TASK_LOCK 
   MODIFY COLUMN EXPIRED_TIME TIMESTAMP(3) NOT NULL,
   MODIFY COLUMN CREATE_TIME TIMESTAMP(3) NOT NULL,
   MODIFY COLUMN UPDATE_TIME TIMESTAMP(3) NOT NULL;
   ```

### 兼容性说明

- 新实现向后兼容原有的数据格式
- 可以逐步迁移，不需要停机
- 支持混合部署（部分节点使用新实现，部分使用原实现）

## 测试验证

运行测试类`EnhancedJDBCTaskLockTest`验证功能：

```bash
mvn test -Dtest=EnhancedJDBCTaskLockTest
```

测试覆盖：
- 基本锁操作（获取、延期、释放）
- 重复锁检测
- 时间精度处理
- 过期锁清理
- 数据库兼容性

## 性能优化建议

1. **定期清理过期锁**：
   ```java
   // 启用自动清理
   timeLockConfig.setEnableExpiredLockCleanup(true);
   timeLockConfig.setExpiredLockCleanupIntervalMinutes(30);
   ```

2. **数据库索引优化**：
   ```sql
   CREATE INDEX idx_expired_time ON T_SCHEDULED_TASK_LOCK (EXPIRED_TIME);
   ```

3. **连接池配置**：
   ```java
   // 使用数据库连接池而不是直接连接
   EnhancedJDBCTaskLockRepositoryImpl repository = 
       new EnhancedJDBCTaskLockRepositoryImpl(timerConfig, dataSource, ...);
   ```

## 监控建议

1. **锁获取成功率监控**
2. **锁持有时间监控**
3. **过期锁数量监控**
4. **数据库连接异常监控**

通过这些优化，可以有效解决原有分布式锁实现中的时间精度、时区转换和数据库兼容性问题，提高系统的稳定性和可靠性。
