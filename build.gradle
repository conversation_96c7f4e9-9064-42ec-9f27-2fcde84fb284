plugins {
    id 'java'
    id 'maven-publish'
    id "de.undercouch.download" version "5.3.0"
}

download.run {
    src 'http://git.koal.com/api/v4/projects/8613/repository/files/properties.gradle/raw?ref=develop'
    dest "${buildDir}/properties.gradle"
    header 'PRIVATE-TOKEN', "${accessToken}"
}

apply from: "${buildDir}/properties.gradle"

sourceCompatibility = 1.8
targetCompatibility = 1.8


repositories {
    mavenLocal()
    maven {
        url mvnPublicRepo
        metadataSources {
            mavenPom()
            artifact()
        }
    }
    mavenCentral()
}

configurations.all {
    // check for updates every build
    resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
    resolutionStrategy.cacheDynamicVersionsFor 0, 'seconds'
}

ext {
    isReleaseVersion = !version.endsWith("SNAPSHOT")
}

configurations {
    testImplementation.extendsFrom compileOnly
}

dependencies {
    implementation "kl.nbase:common-base-helper:${cbbHelperVersion}"
    implementation "kl.nbase:common-base-exception:${cbbExceptionVersion}"
    implementation "org.apache.commons:commons-lang3:${commonsLangVersion}"

    // 以下依赖包以compileOnly引入，由调用方自己提供，比如框架相关的
    compileOnly "org.slf4j:slf4j-api:${slf4jVersion}"
    compileOnly "org.springframework.boot:spring-boot-starter:${springBootVersion}"

    // junit5
    testImplementation "org.junit.jupiter:junit-jupiter-api:${junitVersion}"
    testRuntimeOnly "org.junit.jupiter:junit-jupiter-engine:${junitVersion}"
    testImplementation "org.springframework.boot:spring-boot-starter-test:${springBootVersion}"
    testImplementation "kl.nbase:common-base-config:${cbbConfigVersion}"
    testImplementation "mysql:mysql-connector-java:${mysqlConnectorVersion}"
    testImplementation "com.oracle.database.jdbc:ojdbc8:${oracleOjdbc8Version}"
    testImplementation "cn.com.kingbase:kingbase8:${kingbaseJdbcVersion}"
}


jar {
    enabled = true
    // 忽略重复的文件
    setDuplicatesStrategy(DuplicatesStrategy.EXCLUDE)
    archiveBaseName.set(project.name)
    manifest {
        attributes "Implementation-Title": project.name
        attributes "Implementation-Version": project.version
    }
}

task sourcesJar(type: Jar, dependsOn: classes) {
    // 忽略重复的文件
    setDuplicatesStrategy(DuplicatesStrategy.EXCLUDE)
    archiveClassifier.set('sources')
    archiveBaseName.set(project.name)
    from sourceSets.main.allSource
    from sourceSets.test.allSource
}

artifacts {
    archives jar
    archives sourcesJar
}

publishing {
    repositories {
        maven {
            if (isReleaseVersion) {
                url mvnReleaseRepo
            } else {
                url mvnSnapshotsRepo
            }
            credentials {
                username = mvnUsername
                password = mvnPassword
            }
        }
    }
    publications {
        mavenJava(MavenPublication) {
            from components.java
            artifact sourcesJar
        }
    }
}

test {
    useJUnitPlatform()
}
